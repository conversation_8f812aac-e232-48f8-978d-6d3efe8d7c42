
import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/AdminLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { BookOpen, Users, Package, ShoppingCart, Loader2 } from "lucide-react";
import { dataStore } from "@/lib/store";
import { Category, Book, User, Order } from "@/types";

const AdminDashboard = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [books, setBooks] = useState<Book[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [categoriesData, booksData, usersData, ordersData] = await Promise.all([
        dataStore.getCategories(),
        dataStore.getBooks(),
        dataStore.getUsers(),
        dataStore.getOrders()
      ]);

      setCategories(categoriesData);
      setBooks(booksData);
      setUsers(usersData);
      setOrders(ordersData);
    } catch (error) {
      console.error("Error loading dashboard data:", error);
    } finally {
      setLoading(false);
    }
  };

  const stats = [
    {
      title: "Total Categories",
      value: categories.length,
      icon: Package,
      color: "text-blue-600"
    },
    {
      title: "Total Books",
      value: books.length,
      icon: BookOpen,
      color: "text-green-600"
    },
    {
      title: "Total Users",
      value: users.length,
      icon: Users,
      color: "text-purple-600"
    },
    {
      title: "Total Orders",
      value: orders.length,
      icon: ShoppingCart,
      color: "text-orange-600"
    },
  ];

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">Welcome to Book Rental Admin Panel</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat) => (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
              </CardContent>
            </Card>
          ))}
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading dashboard data...</span>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Orders</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {orders.slice(0, 5).map((order) => {
                    const user = users.find(u => u.id === order.user_id);
                    return (
                      <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <p className="font-medium">Order #{order.id}</p>
                          <p className="text-sm text-muted-foreground">{user?.name || 'Unknown User'}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">Rp {order.total_price.toLocaleString()}</p>
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            order.status === 'approved' ? 'bg-green-100 text-green-800' :
                            order.status === 'rejected' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {order.status}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Popular Books</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {books.slice(0, 5).map((book) => {
                    const category = categories.find(c => c.id === book.category_id);
                    return (
                      <div key={book.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <p className="font-medium">{book.title}</p>
                          <p className="text-sm text-muted-foreground">{book.author}</p>
                          <p className="text-xs text-muted-foreground">{category?.name || 'Unknown Category'}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">Stock: {book.stock}</p>
                          <p className="text-sm text-muted-foreground">Rp {book.price_per_day.toLocaleString()}/day</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
